{"_MENU_": "_MENU_", "Entries Per Page": "Entries Per Page", "Search...": "Search...", "Showing _START_ to _END_ of _TOTAL_ entries": "Showing _START_ to _END_ of _TOTAL_ entries", "Export": "Export", "Print": "Print", "CSV": "CSV", "Excel": "Excel", "Create": "Create", "Reset": "Reset", "Reload": "Reload", "Date": "Date", "Amount": "Amount", "Account": "Account", "Customer": "Customer", "Category": "Category", "Reference": "Reference", "Description": "Description", "Payment Receipt": "Payment Receipt", "No": "No", "Bill": "Bill", "Vendor": "<PERSON><PERSON><PERSON>", "Status": "Status", "Name": "Name", "Contact": "Contact", "Email": "Email", "Balance": "Balance", "From Account": "From Account", "To Account": "To Account", "Invoice": "Invoice", "Account Type": "Account Type", "Bill Date": "<PERSON>", "Due Date": "Due Date", "Due Amount": "Due Amount", "Chart Of Account": "Chart Of Account", "Bank": "Bank", "Account Number": "Account Number", "Current Balance": "Current Balance", "Contact Number": "Contact Number", "Bank Branch": "Bank Branch", "SWIFT": "SWIFT", "Bank Address": "Bank Address", "Permission denied.": "Permission denied.", "The debit note has been created successfully.": "The debit note has been created successfully.", "The debit note details are updated successfully.": "The debit note details are updated successfully.", "The debit note has been deleted.": "The debit note has been deleted.", "Permission Denied.": "Permission Denied.", "Please Enable Product & Service Module": "Please Enable Product & Service Module", " quantity purchase in bill": " quantity purchase in bill", "Bill successfully created.": "<PERSON> successfully created.", "The Bill has been created successfully.": "The Bill has been created successfully.", "Bill Not Found.": "<PERSON> Not Found.", "Bill Not Found!": "Bill Not Found!", "The Bill details are updated successfully.": "The Bill details are updated successfully.", " quantity delete in bill": " quantity delete in bill", "The Bill has been deleted.": "The Bill has been deleted.", "Bill duplicate successfully.": "Bill duplicate successfully.", "Bill successfully sent.": "<PERSON> successfully sent.", "Bill sent email notification is off.": "Bill sent email notification is off.", "Payment successfully added.": "Payment successfully added.", "The payment has been deleted.": "The payment has been deleted.", "The bill product has been deleted.": "The bill product has been deleted.", "The File has been deleted.": "The File has been deleted.", "The revenue has been created successfully.": "The revenue has been created successfully.", "The revenue details are updated successfully.": "The revenue details are updated successfully.", "The revenue has been deleted.": "The revenue has been deleted.", "Cilent Role Not found!": "Cilent Role Not found!", "Something went wrong please try again.": "Something went wrong please try again.", "The customer has been created successfully.": "The customer has been created successfully.", "The customer details are updated successfully.": "The customer details are updated successfully.", "The customer has been deleted.": "The customer has been deleted.", "User is not converted into vendor.": "User is not converted into vendor.", "The credit note has been created successfully.": "The credit note has been created successfully.", "The credit note details are updated successfully.": "The credit note details are updated successfully.", "The credit note has been deleted.": "The credit note has been deleted.", "Credit Note successfully created.": "Credit Note successfully created.", "User is not converted into customer.": "User is not converted into customer.", "The invoice field is required.": "The invoice field is required.", "All": "All", "Revenue": "Revenue", "Payment": "Payment", "Jan-Mar": "Jan-<PERSON>", "Apr-Jun": "Apr-Jun", "Jul-Sep": "Jul-Sep", "Oct-Dec": "Oct-Dec", "Total": "Total", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "The payment has been created successfully.": "The payment has been created successfully.", "The payment details are updated successfully.": "The payment details are updated successfully.", "The account has been created successfully.": "The account has been created successfully.", "The account details are updated successfully.": "The account details are updated successfully.", "The account has been deleted.": "The account has been deleted.", "Stripe / Paypal": "Stripe / Paypal", "Bank Account Not Found!": "Bank Account Not Found!", "Please delete related record of this account.": "Please delete related record of this account.", "Bank Accounts Setting saved successfully": "Bank Accounts Setting saved successfully", "Invoice payment request send successfully": "Invoice payment request send successfully", "Your request will be approved by company and then your payment will be activated.": "Your request will be approved by company and then your payment will be activated.", "Sales Invoice payment request send successfully": "Sales Invoice payment request send successfully", "Retainer payment request send successfully": "Retainer payment request send successfully", "Request data not found!": "Request data not found!", "Bank Account request Approve successfully": "Bank Account request Approve successfully", "Bank Account request Reject successfully": "Bank Account request Reject successfully", "The Bank Account request has been deleted.": "The Bank Account request has been deleted.", "The amount has been created successfully.": "The amount has been created successfully.", "The amount details are updated successfully.": "The amount details are updated successfully.", "The amount transfer has been deleted.": "The amount transfer has been deleted.", "Vendor Role Not found !": "V<PERSON>or Role Not found !", "The vendor has been created successfully.": "The vendor has been created successfully.", "The vendor details are updated successfully.": "The vendor details are updated successfully.", "The vendor has been deleted.": "The vendor has been deleted.", "Accounting Dashboard": "Accounting Dashboard", "Accounting": "Accounting", "Banking": "Banking", "Chart Of Accounts": "Chart Of Accounts", "Transfer": "Transfer", "Income": "Income", "Credit Notes": "Credit Notes", "Expense": "Expense", "Debit Notes": "Debit Notes", "Report": "Report", "Transaction": "Transaction", "Account Statement": "Account Statement", "Income Summary": "Income Summary", "Expense Summary": "Expense Summary", "Income Vs Expense": "Income Vs Expense", "Tax Summary": "Tax Summary", "Profit & Loss": "Profit & Loss", "Invoice Summary": "Invoice Summary", "Bill Summary": "<PERSON>", "Product Stock": "Product Stock", "Cash Flow": "Cash Flow", "Account Settings": "Account <PERSON><PERSON>", "Bill Print Settings": "Bill Print Settings", "Bank Accounts": "Bank Accounts", "Cancel": "Cancel", "Save Changes": "Save Changes", "Manage Credit Notes": "Manage Credit Notes", "Dashboard": "Dashboard", "Credit Note": "Credit Note", "Create New Credit Note": "Create New Credit Note", "Action": "Action", "Edit Credit Note": "Edit Credit Note", "Edit": "Edit", "Are You Sure?": "Are You Sure?", "This action can not be undone. Do you want to continue?": "This action can not be undone. Do you want to continue?", "Apply": "Apply", "Select Invoice": "Select Invoice", "Edit Debit Note": "Edit Debit Note", "Delete": "Delete", "Manage Debit Notes": "Manage Debit Notes", "Debit Note": "Debit Note", "Create New Debit Note": "Create New Debit Note", "Add": "Add", "Select Bill": "Select Bill", "From Type": "From Type", "Select Type": "Select Type", "Wallet": "Wallet", "To Type": "To Type", "Enter Amount": "Enter Amount", "Enter Description": "Enter Description", "Edit Transfer": "Edit Transfer", "Bank Balance Transfer": "Bank Balance Transfer", "Create New Transfer": "Create New Transfer", "apply": "apply", "Items": "Items", "Add item": "Add item", "Item Type": "Item Type", "Quantity": "Quantity", "Price": "Price", "Discount": "Discount", "Tax": "Tax", "After discount & tax": "After discount & tax", "Please create Product first.": "Please create Product first.", "Add Product": "Add Product", "Qty": "Qty", "Sub Total": "Sub Total", "Total Amount": "Total Amount", "Select Item": "Select Item", "Credit Note Summary": "Credit Note Summary", "Software Details": "Software Details", "Account is an account management software that facilitates ease in revenue calculation by keeping a tab on all the accountancy matters of business. Based on Laravel, this accountancy software will make your business operations smooth and convenient. A graphical and tabular representation of various elements will help you make informed decisions for your firm.": "Account is an account management software that facilitates ease in revenue calculation by keeping a tab on all the accountancy matters of business. Based on Lara<PERSON>, this accountancy software will make your business operations smooth and convenient. A graphical and tabular representation of various elements will help you make informed decisions for your firm.", "/Month": "/Month", "/Year": "/Year", "Buy Now": "Buy Now", "View Live Demo": "View Live Demo", "ACCOUNTING": "ACCOUNTING", "AND": "AND", "BILLING, SIMPLIFIED": "BILLING, SIMPLIFIED", "Accounting gives you the power to keep an eye on all accounting and inventory matters from one tab. You’ll never have to manage accounting with one tool, and control inventory with another - ever again!": "Accounting gives you the power to keep an eye on all accounting and inventory matters from one tab. You’ll never have to manage accounting with one tool, and control inventory with another - ever again!", "Account Helps You Simplify Your Accounting and Billing": "Account Helps You Simplify Your Accounting and Billing", "Simplify Your Accounting and Billing": "Simplify Your Accounting and Billing", "Simplify your accounting and make it easy to keep an eye on your money. Set financial goals and let the system monitor them for you, automate taxes, and more! - without lifting a finger.": "Simplify your accounting and make it easy to keep an eye on your money. Set financial goals and let the system monitor them for you, automate taxes, and more! - without lifting a finger.", "Take Control Of Your Inventory": "Take Control Of Your Inventory", "Save time by managing your entire inventory with a few clicks. Easily create categories and add products to them. Modify product prices whenever you want, assign SKUs, create different tax rates, and do so much more!": "Save time by managing your entire inventory with a few clicks. Easily create categories and add products to them. Modify product prices whenever you want, assign SKUs, create different tax rates, and do so much more!", "Take Your Project from Proposal to Payment": "Take Your Project from Proposal to Payment", "Land new clients in a flash, and get paid just as fast. Create proposal templates and pitch your future clients. Turn your accepted proposals into payable invoices, send reminders, and get paid fast - all in one place!": "Land new clients in a flash, and get paid just as fast. Create proposal templates and pitch your future clients. Turn your accepted proposals into payable invoices, send reminders, and get paid fast - all in one place!", "Get All the Critical Tools to Manage Business Finances": "Get All the Critical Tools to Manage Business Finances", "Manage Accounting Quickly and Easily": "Manage Accounting Quickly and Easily", "Manage your billing and accounting without little to no effort! Set financial goals and let the system monitor them for you, automate taxes, and more! - without lifting a finger.": "Manage your billing and accounting without little to no effort! Set financial goals and let the system monitor them for you, automate taxes, and more! - without lifting a finger.", "Handle Inventory Tasks Without Stress": "Handle Inventory Tasks Without Stress", "Easily manage inventory by creating categories and adding products to them. Modify product prices whenever you want, assign SKUs, create different tax rates, and do so much more!": "Easily manage inventory by creating categories and adding products to them. Modify product prices whenever you want, assign SKUs, create different tax rates, and do so much more!", "Take Your Project From Proposal To Payment": "Take Your Project From Proposal To Payment", "Create proposal templates and pitch your future clients. Turn your accepted proposals into payable invoices, send reminders, and get paid fast - all in one place!": "Create proposal templates and pitch your future clients. Turn your accepted proposals into payable invoices, send reminders, and get paid fast - all in one place!", "Reports": "Reports", "Get a report on transactions with an easy filtering option. Download the required account statements in either PDF, CSV, or Excel format. You get duly prepared reports on individual Income, Expenses, Tax, Invoice, and bill summary. Filter them based on Account, category, and customers. Also, a graphical display of the Income VS Expense chart along with a detailed calculation of Profit and Loss will help you make informed decisions. Filter the tax summary and Income VS Expense chart based on financial years.": "Get a report on transactions with an easy filtering option. Download the required account statements in either PDF, CSV, or Excel format. You get duly prepared reports on individual Income, Expenses, Tax, Invoice, and bill summary. Filter them based on Account, category, and customers. Also, a graphical display of the Income VS Expense chart along with a detailed calculation of Profit and Loss will help you make informed decisions. Filter the tax summary and Income VS Expense chart based on financial years.", "Budget Planner": "Budget Planner", "A budget is a financial plan for a specified period to keep in check with the working capital. This feature here helps to maintain the capital flow. You can set monthly, quarterly, half-yearly, or yearly budgets according to your business plans and needs. The main categories are “Income” and “Expense” where one can edit /update /delete the sub-categories as well.": "A budget is a financial plan for a specified period to keep in check with the working capital. This feature here helps to maintain the capital flow. You can set monthly, quarterly, half-yearly, or yearly budgets according to your business plans and needs. The main categories are “Income” and “Expense” where one can edit /update /delete the sub-categories as well.", "Why choose dedicated modules": "Why choose dedicated modules", "for your business?": "for your business?", "With Dash, you can conveniently manage all your business functions from a single location": "With <PERSON>, you can conveniently manage all your business functions from a single location", "Empower Your Workforce with DASH": "Empower Your Workforce with DASH", "Access over Premium Add-ons for Accounting, HR, Payments, Leads, Communication, Management, and more, all in one place!": "Access over Premium Add-ons for Accounting, HR, Payments, Leads, Communication, Management, and more, all in one place!", "Pay-as-you-go": "Pay-as-you-go", "Unlimited installation": "Unlimited installation", "Secure cloud storage": "Secure cloud storage", "Monthly": "Monthly", "Yearly": "Yearly", "Billed monthly, or": "Billed monthly, or", " if paid monthly": " if paid monthly", "for Your Business?": "for Your Business?", "With Dash, you can conveniently manage all your business functions from a single location.": "With <PERSON>, you can conveniently manage all your business functions from a single location.", "Customers": "Customers", "Vendors": "Vend<PERSON>", "Invoices": "Invoices", "Bills": "Bills", "Account Balance": "Account <PERSON><PERSON>", "Holder Name": "Holder Name", "there is no account balance": "there is no account balance", "Cashflow": "Cashflow", "Income Today": "Income Today", "Expense Today": "Expense Today", "Income This Month": "Income This Month", "Expense This Month": "Expense This Month", "Income & Expense": "Income & Expense", "Current Year": "Current Year", "Income By Category": "Income By Category", "Year": "Year", "Latest Income": "Latest Income", "Amount Due": "Amount Due", "Expense By Category": "Expense By Category", "Latest Expense": "Latest Expense", "Recent Invoices": "Recent Invoices", "Issue Date": "Issue Date", "Invoices Weekly Statistics": "Invoices Weekly Statistics", "Invoices Monthly Statistics": "Invoices Monthly Statistics", "Invoice Generated": "Invoice Generated", "Paid": "Paid", "Due": "Due", "Recent Bills": "Recent Bills", "Bills Weekly Statistics": "Bills Weekly Statistics", "Bills Monthly Statistics": "Bills Monthly Statistics", "Bill Generated": "<PERSON>", "Months": "Months", "Download": "Download", "Duration": "Duration", "Revenue :": "Revenue :", "Invoice :": "Invoice :", "Income = Revenue + Invoice :": "Income = Revenue + Invoice :", "Quarterly": "Quarterly", "Monthly Cashflow": "Monthly Cashflow", "Revenue : ": "Revenue : ", "Invoice : ": "Invoice : ", "Total Income =  Revenue + Invoice ": "Total Income =  Revenue + Invoice ", "Total Income": "Total Income", "PaySlip": "PaySlip", "Total Expense =  Payment + PaySlip + Bill ": "Total Expense =  Payment + PaySlip + Bill ", "Total Expenses": "Total Expenses", "Net Profit = Total Income - Total Expense": "Net Profit = Total Income - Total Expense", "Net Profit": "Net Profit", "Income Vs Expense Summary": "Income Vs Expense Summary", "Profit": "Profit", "Type": "Type", "Income : ": "Income : ", "Expense : ": "Expense : ", "Purchase": "Purchase", "Employee Salary": "Employee Salary", "Profit = Income - Expense ": "Profit = Income - Expense ", "Income tax not found": "Income tax not found", "Expense tax not found": "Expense tax not found", "Payment :": "Payment :", "Bill :": "Bill :", "Purchase :": "Purchase :", "Employee Salary :": "Employee Salary :", "Expense = Payment + Bill + Employee Salary :": "Expense = Payment + Bill + Employee Salary :", "Expense = Payment + Bill + Purchase :": "Expense = Payment + Bill + Purchase :", "Product Stock Report": "Product Stock Report", "Product Name": "Product Name", "Product & Service Module is Off": "Product & Service Module is Off", "Profit & Loss Summary": "Profit & Loss Summary", "Profit && Loss Summary": "Profit && Loss Summary", "Payment : ": "Payment : ", "Bill : ": "Bill : ", "Total Expense =  Payment + Bill + Employee Salary": "Total Expense =  Payment + Bill + Employee Salary", "Total Expense =  Payment + Bill": "Total Expense =  Payment + Bill", "Net Profit = Total Income - Total Expense ": "Net Profit = Total Income - Total Expense ", "Quarterly Cashflow": "Quarterly Cashflow", "Start Month": "Start Month", "End Month": "End Month", "of": "of", "Total Invoice": "Total Invoice", "Total Paid": "Total Paid", "Total Due": "Total Due", "Summary": "Summary", "\tPaid Amount": "\t<PERSON><PERSON>", "Payment Date": "Payment Date", "Account Statement Summary": "Account Statement Summary", "Bill / Purchase": "Bill / Purchase", "Total Bill": "Total Bill", "Paid Amount": "<PERSON><PERSON>", "Bank Details": "Bank Details", "Bank Name": "Bank Name", "Please On Your Bank Account!": "Please On Your Bank Account!", "Click Here": "Click Here", " Remove": " Remove", "Choose file here": "Choose file here", "Edit Payment": "Edit Payment", "Bank Account": "Bank Account", "This field is required": "This field is required", "first, make a payment and take a screenshot or download the receipt and upload it.": "first, make a payment and take a screenshot or download the receipt and upload it.", "Please correct the errors and try again.": "Please correct the errors and try again.", "Close": "Close", "Make Payment": "Make Payment", "Manage Payments": "Manage Payments", "Setup": "Setup", "Create New Payment": "Create New Payment", "Invoice Number": "Invoice Number", "Order Id": "Order Id", "status": "status", "Payment Type": "Payment Type", "Bank Detail": "Bank Detail", "Account Holder Name": "Account Holder Name", "Attachment": "Attachment", "Preview": "Preview", "Not Found": "Not Found", "Approved": "Approved", "Reject": "Reject", "Basic Info": "Basic Info", "Enter Customer Name": "Enter Customer Name", "Enter Contact": "Enter Contact", "Tax Number": "Tax Number", "Enter Tax Number": "Enter Tax Number", "Billing Address": "Billing Address", "Enter Name": "Enter Name", "Phone": "Phone", "Enter Phone": "Enter Phone", "Address": "Address", "Enter Address": "Enter Address", "City": "City", "Enter City": "Enter City", "State": "State", "Enter State": "Enter State", "Country": "Country", "Enter Country": "Enter Country", "Zip Code": "Zip Code", "Enter Zip Code": "Enter Zip Code", "Shipping Same As Billing": "Shipping Same As Billing", "Shipping Address": "Shipping Address", "View": "View", "Edit Customer": "Edit Customer", "Manage Customers": "Manage Customers", "Customer Import": "Customer Import", "Import": "Import", "Grid View": "Grid View", "Create New Customer": "Create New Customer", "Customer Statement": "Customer Statement", "Statement": "Statement", "From Date": "From Date", "Until Date": "Until Date", "My Company": "My Company", "Statement of Account": "Statement of Account", "Billed To": "Billed To", "Tax Number ": "Tax Number ", "Shipped To": "Shipped To", "No Data Found": "No Data Found", "TOTAL :": "TOTAL :", "Download Sample Customer CSV File": "Download Sample Customer CSV File", "Select CSV File": "Select CSV File", "Upload": "Upload", "Import Customer CSV Data": "Import Customer CSV Data", "Customer-Detail": "Customer-Detail", "Create Invoice": "Create Invoice", "Create Proposal": "Create Proposal", "Create retainer": "Create retainer", "Details": "Details", "Proposals": "Proposals", "Customer Info": "Customer Info", "Billing Info": "Billing Info", "Shipping Info": "Shipping Info", "Company Info": "Company Info", "Customer Id": "Customer Id", "Total Sum of Invoices": "Total Sum of Invoices", "Date of Creation": "Date of Creation", "Quantity of Invoice": "Quantity of Invoice", "Average Sales": "Average Sales", "Overdue": "Overdue", "Proposal": "Proposal", "Convert to Invoice": "Convert to Invoice", "Already convert to Invoice": "Already convert to Invoice", "Duplicate": "Duplicate", "You want to confirm duplicate this invoice. Press Yes to continue or Cancel to go back": "You want to confirm duplicate this invoice. Press Yes to continue or <PERSON><PERSON> to go back", "Show": "Show", "Detail": "Detail", "Duplicate Invoice": "Duplicate Invoice", "You want to confirm this action. Press Yes to continue or Cancel to go back": "You want to confirm this action. Press Yes to continue or Can<PERSON> to go back", "Edit Revenue": "Edit Revenue", "Account summary": "Account summary", "Invoiced amount": "Invoiced amount", "Amount Paid": "Amount <PERSON>", "Balance Due": "Balance Due", "Please add contact number with country code. (ex. +91)": "Please add contact number with country code. (ex. +91)", "Password": "Password", "List View": "List View", "Edit customer": "Edit customer", "Customer Details": "Customer Details", "Code": "Code", "Is Enabled": "Is Enabled", "Manage Chart of Accounts": "Manage Chart of Accounts", "Chart of Account": "Chart of Account", "Create New Account": "Create New Account", "Start Date": "Start Date", "End Date": "End Date", "Parent Account Name": "Parent Account Name", "Enabled": "Enabled", "Disabled": "Disabled", "Edit Account": "Edit Account", "Account Drilldown Report": "Account Drilldown Report", "Account Drilldown": "Account <PERSON><PERSON><PERSON>", "Account Name": "Account Name", "Account Code": "Account Code", "Transaction Type": "Transaction Type", "Transaction Date": "Transaction Date", "Debit": "Debit", "Credit": "Credit", "Make this a sub-account": "Make this a sub-account", "Parent Account": "Parent Account", "Bill Edit": "<PERSON>", "something went wrong please try again": "something went wrong please try again", "Project": "Project", "Select Account Type": "Select Account Type", "Projects": "Projects", "Please create vendor/Client first.": "Please create vendor/Client first.", "Create vendor/Client": "Create vendor/Client", "Billing Type": "Billing Type", "Select Billing Type": "Select Billing Type", "Item Wise": "<PERSON><PERSON>", "Project Wise": "Project Wise", "bill Number": "bill Number", "Order Number": "Order Number", "Manage Bill": "Manage Bill", "Link Copy on Clipboard": "<PERSON>py on Clipboard", "Copy": "Copy", "Click to copy Bill link": "Click to copy <PERSON> link", "BILL": "BILL", "Registration Number": "Registration Number", "Number": "Number", "Number: ": "Number: ", "Issue Date:": "Issue Date:", "Bill To": "Bill <PERSON>", "Ship To": "Ship To", "Item": "<PERSON><PERSON>", "Rate": "Rate", "Subtotal": "Subtotal", " FROM": " FROM", "From:": "From:", "FROM": "FROM", "Manage Bills": "Manage Bills", "Bill Detail": "<PERSON>", "Add Debit Note": "Add Debit Note", "Resend Bill": "Resend Bill", "Send Bill": "Send Bill", "SendMail": "SendMail", "Item Summary": "<PERSON><PERSON>", "All items here cannot be deleted.": "All items here cannot be deleted.", "after discount & tax": "after discount & tax", "Payment Summary": "Payment Summary", "Debit Note Summary": "Debit Note Summary", "Success": "Success", "copy": "copy", "Click to copy invoice link": "Click to copy invoice link", "Create Bill": "Create Bill", "Created on ": "Created on ", "Sent on": "<PERSON>t on", "Not Sent": "Not Sent", "Mark Sent": "<PERSON>", "Send": "Send", "Pay Bill": "Pay Bill", "Awaiting payment": "Awaiting payment", "Add Payment": "Add Payment", "Apply Debit Note": "Apply Debit Note", "Send Mail": "Send Mail", "Account Amount": "Account Amount", "Debit note Applied": "Debit note Applied", "Debit note issued": "Debit note issued", "Attachments": "Attachments", "Drop files here to upload": "Drop files here to upload", "File Name": "File Name", "File Size": "File Size", "Date Created": "Date Created", "Bill Create": "<PERSON>", "vendor": "vendor", "Bill Number": "<PERSON>", "Please add constant category. ": "Please add constant category. ", "Add Category": "Add Category", "Click to copy bill link": "Click to copy bill link", "Click here to add Bill": "Click here to add Bill", "Bill to": "Bill to", "Ship to": "Ship to", "BIlling Address": "BIlling Address", "Edit Vendor": "<PERSON>", "Manage Vendors": "Manage Vendors", "Vendor Import": "Vendor <PERSON>", "Create New Vendor": "Create <PERSON>or", "Vendor Statement": "Vendor Statement", "Download Sample Vendor CSV File": "Download Sample Vendor CSV File", "Import Vendor CSV Data": "Import Vendor CSV Data", "Vendor-Detail": "Vendor-Detail", "Vendor Info": "Vendor Info", "Vendor Id": "Vendor Id", "Total Sum of Bills": "Total Sum of Bills", "Quantity of Bills": "Quantity of Bills", "Duplicate Bill": "Duplicate Bill", "Billed amount": "Billed amount", "Edit vendor": "Edit vendor", "vendor Details": "vendor Details", "Bank Type": "Bank Type", "Paypal": "<PERSON><PERSON>", "Stripe": "Stripe", "Bank Holder Name": "Bank Holder Name", "Enter Bank Holder Name": "Enter Bank Holder Name", "Enter Bank Name": "Enter Bank Name", "Enter Account Number": "Enter Account Number", "Opening Balance": "Opening Balance", "Enter Opening Balance": "Enter Opening Balance", "Enter Contact Number": "Enter Contact Number", "Enter Bank Branch": "Enter Bank Branch", "Enter Swift Number": "Enter Swift Number", "Enter Bank Address": "Enter Bank Address", "Edit Bank Account": "Edit Bank Account", "Manage Bank Account": "Manage Bank Account", "Transaction Summary": "Transaction Summary", "Manage Revenues": "Manage Revenues", "Revenues": "Revenues", "Create New Revenue": "Create New Revenue", "Income Account": "Income Account", "Expense Account": "Expense Account", "Customer Prefix": "Customer Prefix", "Vendor Prefix": "<PERSON><PERSON><PERSON>", "Edit your Company Bill details": "Edit your Company Bill details", "Prefix": "Prefix", "Starting Number": "Starting Number", "Footer Title": "Footer Title", "Footer Notes": "Footer Notes", "Shipping Display?": "Shipping Display?", "QR Display?": "QR Display?", "Bill Template": "<PERSON>", "Color Input": "Color Input", "Bill Logo": "<PERSON>", "Edit Bank Accounts settings": "Edit Bank Accounts settings", "Account number": "Account number", "Enable/Disable": "Enable/Disable"}