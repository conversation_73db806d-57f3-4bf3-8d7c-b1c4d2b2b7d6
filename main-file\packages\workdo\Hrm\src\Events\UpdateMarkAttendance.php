<?php

namespace Workdo\Hrm\Events;

use Illuminate\Queue\SerializesModels;

class UpdateMarkAttendance
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */

    public $request;
    public $attendance;

    public function __construct($request, $attendance)
    {
        $this->request = $request;
        $this->attendance = $attendance;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
