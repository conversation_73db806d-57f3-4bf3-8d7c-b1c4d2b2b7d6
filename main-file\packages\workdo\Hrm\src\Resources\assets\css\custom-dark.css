.set-card {
    height: 300px !important;
    overflow: auto;

}

.doc-description {
    white-space: break-spaces !important;

}
.drp-language .dropdown-toggle{
    color: #525B69;
    display: inline-flex;
    align-items: center;
    padding: 0.6rem 0.7rem;
    margin: 0 7.5px;
    border-radius: 4px;
    position: relative;
    font-weight: 500;
    border-radius: 12px;
    border: 1px solid rgba(206, 206, 206, 0.2);
}

.emp-card {
    min-height: 265px !important;
}

/* employee show css */
.employee-card .card .info.font-style{
    margin-bottom: 10px;
}
.employee-card .card .info.font-style strong{
    font-weight: 500;
    margin-right: 5px;
}

.employee-card-header ul.nav li.nav-item button{
    background: #0CAF601A;
    border-radius: 10px;
}

.employee-card-header ul.nav {
    gap: 10px;
    background: #292a33;
    padding: 10px;
}
.employee-card .card .card-header:not(.border-0) h5:after, .card .card-header:not(.border-0) .h5:after{
    display: none;
}
.employee-card-header  .nav-pills .nav-link:focus, 
.employee-card-header  .nav-pills .nav-link.active, 
.employee-card-header  .nav-pills .show > .nav-link{
    color: #ffffff !important;
}

@media screen and (min-width: 768px){
    .employee-card  .card-body{
        position: relative;
    }
    .employee-card .card-body:not(.table-border-style)::before{
        content: "";
        top: 50%;
        left: 0;
        right: 0;
        margin: 0 auto;
        width: 2px;
        height: 50%;
        position: absolute;
        background: linear-gradient(47deg, #292a33 0%, #0caf608c 50%, #292a33 100%);
        border-radius: 50%;
        -webkit-border-radius: 50%;
        -moz-border-radius: 50%;
        -ms-border-radius: 50%;
        -o-border-radius: 50%;
        transform: translate(0 , -50%);
    }
}