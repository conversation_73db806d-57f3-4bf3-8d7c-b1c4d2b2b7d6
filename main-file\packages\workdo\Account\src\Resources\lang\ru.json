{"_MENU_": "_МЕНЮ_", "Entries Per Page": "Записей на странице", "Search...": "Поиск...", "Showing _START_ to _END_ of _TOTAL_ entries": "Показаны от _START_ до _END_ из _TOTAL_ записей", "Export": "Экспорт", "Print": "Распечатать", "CSV": "CSV-файл", "Excel": "Эксель", "Create": "Создавать", "Reset": "Перезагрузить", "Reload": "Перезагрузить", "Date": "Дата", "Amount": "Количество", "Account": "Счет", "Customer": "Кли<PERSON><PERSON>т", "Category": "Категория", "Reference": "Ссылка", "Description": "Описание", "Payment Receipt": "Квитанция об оплате", "No": "Нет", "Bill": "Счет", "Vendor": "Продавец", "Status": "Статус", "Name": "Имя", "Contact": "Кон<PERSON><PERSON><PERSON>т", "Email": "Электронная почта", "Balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "From Account": "Из аккаунта", "To Account": "На счет", "Invoice": "Счет", "Account Type": "Тип учетной записи", "Bill Date": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>а", "Due Date": "Срок оплаты", "Due Amount": "Сумма к оплате", "Chart Of Account": "<PERSON><PERSON><PERSON><PERSON> счетов", "Bank": "<PERSON><PERSON><PERSON><PERSON>", "Account Number": "Номер счета", "Current Balance": "Текущий баланс", "Contact Number": "Контактный номер", "Bank Branch": "Филиа<PERSON>н<PERSON>а", "SWIFT": "БЫСТРЫЙ", "Bank Address": "Адрес банка", "Permission denied.": "Доступ запрещен.", "The debit note has been created successfully.": "Дебетовое авизо успешно создано.", "The debit note details are updated successfully.": "Детали дебетового авизо успешно обновлены.", "The debit note has been deleted.": "Дебетовое авизо удалено.", "Permission Denied.": "Доступ запрещен.", "Please Enable Product & Service Module": "Пожалуйста, включите модуль продуктов и услуг", " quantity purchase in bill": " покупка количества в счете", "Bill successfully created.": "Билл успешно создан.", "The Bill has been created successfully.": "Законопроект успешно создан.", "Bill Not Found.": "Билл не найден.", "Bill Not Found!": "Билл не найден!", "The Bill details are updated successfully.": "Сведения о счете успешно обновлены.", " quantity delete in bill": " количество удалить в счете", "The Bill has been deleted.": "Законопроект удален.", "Bill duplicate successfully.": "Дублирование счета успешно выполнено.", "Bill successfully sent.": "Счет успешно отправлен.", "Bill sent email notification is off.": "Уведомление об отправке счета по электронной почте отключено.", "Payment successfully added.": "Платеж успешно добавлен.", "The payment has been deleted.": "Платеж удален.", "The bill product has been deleted.": "Продукт счета был удален.", "The File has been deleted.": "Файл был удален.", "The revenue has been created successfully.": "Доход был успешно создан.", "The revenue details are updated successfully.": "Сведения о доходе успешно обновлены.", "The revenue has been deleted.": "Доход удален.", "Cilent Role Not found!": "Силент Роль Не найдена!", "Something went wrong please try again.": "Что-то пошло не так, попробуйте еще раз.", "The customer has been created successfully.": "Клиент успешно создан.", "The customer details are updated successfully.": "Сведения о клиенте успешно обновлены.", "The customer has been deleted.": "Клиент удален.", "User is not converted into vendor.": "Пользователь не конвертируется в поставщика.", "The credit note has been created successfully.": "Кредит-нота успешно создана.", "The credit note details are updated successfully.": "Детали кредит-ноты успешно обновлены.", "The credit note has been deleted.": "Кредитная нота была удалена.", "Credit Note successfully created.": "Кредит-нота успешно создана.", "User is not converted into customer.": "Пользователь не конвертируется в клиента.", "The invoice field is required.": "Поле счета-фактуры является обязательным.", "All": "Все", "Revenue": "Доход", "Payment": "Оплата", "Jan-Mar": "<PERSON><PERSON><PERSON>-<PERSON>а<PERSON>т", "Apr-Jun": "апрель-июнь", "Jul-Sep": "июль-сентябрь", "Oct-Dec": "октябрь-декабрь", "Total": "Общий", "January": "январь", "February": "февраль", "March": "М<PERSON><PERSON><PERSON><PERSON><PERSON>овать", "April": "апрель", "May": "<PERSON>о<PERSON><PERSON><PERSON>", "June": "Июнь", "July": "Июль", "August": "Август", "September": "Сентябрь", "October": "Октябрь", "November": "ноябрь", "December": "декабрь", "The payment has been created successfully.": "Платеж успешно создан.", "The payment details are updated successfully.": "Детали платежа успешно обновлены.", "The account has been created successfully.": "Аккаунт успешно создан.", "The account details are updated successfully.": "Данные учетной записи успешно обновлены.", "The account has been deleted.": "Аккаунт удален.", "Stripe / Paypal": "Полоса / PayPal", "Bank Account Not Found!": "Банковский счет не найден!", "Please delete related record of this account.": "Пожалуйста, удалите соответствующую запись этого аккаунта.", "Bank Accounts Setting saved successfully": "Настройки банковских счетов успешно сохранены.", "Invoice payment request send successfully": "Запрос на оплату счета успешно отправлен", "Your request will be approved by company and then your payment will be activated.": "Ваш запрос будет одобрен компанией, после чего ваш платеж будет активирован.", "Sales Invoice payment request send successfully": "Запрос на оплату счета за продажу успешно отправлен", "Retainer payment request send successfully": "Запрос на предоплату отправлен успешно", "Request data not found!": "Данные запроса не найдены!", "Bank Account request Approve successfully": "Запрос на банковский счет успешно одобрен", "Bank Account request Reject successfully": "Запрос банковского счета успешно отклонен", "The Bank Account request has been deleted.": "Запрос банковского счета был удален.", "The amount has been created successfully.": "Сумма успешно создана.", "The amount details are updated successfully.": "Сведения о сумме успешно обновлены.", "The amount transfer has been deleted.": "Сумма перевода удалена.", "Vendor Role Not found !": "Роль поставщика не найдена!", "The vendor has been created successfully.": "Поставщик успешно создан.", "The vendor details are updated successfully.": "Сведения о поставщике успешно обновлены.", "The vendor has been deleted.": "Поставщик удален.", "Accounting Dashboard": "Панель учета", "Accounting": "Бухгалтерский учет", "Banking": "Банковское дело", "Chart Of Accounts": "<PERSON><PERSON><PERSON><PERSON> счетов", "Transfer": "Передача", "Income": "Доход", "Credit Notes": "Кредитные ноты", "Expense": "Расход", "Debit Notes": "Дебетовые примечания", "Report": "Отчет", "Transaction": "Сделка", "Account Statement": "Выписка со счета", "Income Summary": "Сводка доходов", "Expense Summary": "Сводка расходов", "Income Vs Expense": "Доходы против расходов", "Tax Summary": "Налоговая сводка", "Profit & Loss": "Прибыль и убытки", "Invoice Summary": "Сводка счетов", "Bill Summary": "Краткое изложение законопроекта", "Product Stock": "Товар на складе", "Cash Flow": "Денежный поток", "Account Settings": "Настройки учетной записи", "Bill Print Settings": "Настройки печати счетов", "Bank Accounts": "Банковские счета", "Cancel": "Отмена", "Save Changes": "Сохранить изменения", "Manage Credit Notes": "Управление кредит-нотами", "Dashboard": "Панель управления", "Credit Note": "Кредитная нота", "Create New Credit Note": "Создать новую кредитную ноту", "Action": "Действие", "Edit Credit Note": "Редактировать кредит-ноту", "Edit": "Редактировать", "Are You Sure?": "Вы уверены?", "This action can not be undone. Do you want to continue?": "Это действие невозможно отменить. ", "Apply": "Применять", "Select Invoice": "Выберите счет", "Edit Debit Note": "Редактировать дебетовое авизо", "Delete": "Удалить", "Manage Debit Notes": "Управление дебетовыми нотами", "Debit Note": "Дебетовая записка", "Create New Debit Note": "Создать новую дебетовую записку", "Add": "Добавлять", "Select Bill": "Выберите Билла", "From Type": "Из типа", "Select Type": "Выберите тип", "Wallet": "Кошелек", "To Type": "Для ввода", "Enter Amount": "Введите сумму", "Enter Description": "Введите описание", "Edit Transfer": "Редактировать трансфер", "Bank Balance Transfer": "Банковский перевод баланса", "Create New Transfer": "Создать новый перевод", "apply": "применять", "Items": "Предметы", "Add item": "Добавить элемент", "Item Type": "Тип элемента", "Quantity": "Количество", "Price": "Цена", "Discount": "Скидка", "Tax": "Налог", "After discount & tax": "После скидки и налога", "Please create Product first.": "Пожалуйста, сначала создайте продукт.", "Add Product": "Добавить продукт", "Qty": "Кол-во", "Sub Total": "Промежуточный итог", "Total Amount": "Общая сумма", "Select Item": "Выберите элемент", "Credit Note Summary": "Сводная информация по кредит-ноте", "Software Details": "Подробности программного обеспечения", "Account is an account management software that facilitates ease in revenue calculation by keeping a tab on all the accountancy matters of business. Based on Laravel, this accountancy software will make your business operations smooth and convenient. A graphical and tabular representation of various elements will help you make informed decisions for your firm.": "Account — это программное обеспечение для управления счетами, которое упрощает расчет доходов, отслеживая все бухгалтерские вопросы бизнеса. ", "/Month": "/Месяц", "/Year": "/Год", "Buy Now": "Купить сейчас", "View Live Demo": "Посмотреть живую демонстрацию", "ACCOUNTING": "УЧЕТ", "AND": "И", "BILLING, SIMPLIFIED": "БИЛЛИНГ, УПРОЩЕННЫЙ", "Accounting gives you the power to keep an eye on all accounting and inventory matters from one tab. You’ll never have to manage accounting with one tool, and control inventory with another - ever again!": "Бухгалтерский учет дает вам возможность следить за всеми вопросами бухгалтерского учета и инвентаризации с одной вкладки. ", "Account Helps You Simplify Your Accounting and Billing": "Учетная запись помогает вам упростить учет и выставление счетов", "Simplify Your Accounting and Billing": "Упростите учет и выставление счетов", "Simplify your accounting and make it easy to keep an eye on your money. Set financial goals and let the system monitor them for you, automate taxes, and more! - without lifting a finger.": "Упростите учет и упростите контроль за своими деньгами. ", "Take Control Of Your Inventory": "Возьмите под контроль свой инвентарь", "Save time by managing your entire inventory with a few clicks. Easily create categories and add products to them. Modify product prices whenever you want, assign SKUs, create different tax rates, and do so much more!": "Экономьте время, управляя всем своим инвентарем с помощью нескольких щелчков мыши. ", "Take Your Project from Proposal to Payment": "Проведите свой проект от предложения до оплаты", "Land new clients in a flash, and get paid just as fast. Create proposal templates and pitch your future clients. Turn your accepted proposals into payable invoices, send reminders, and get paid fast - all in one place!": "Мгновенно привлекайте новых клиентов и получайте оплату так же быстро. ", "Get All the Critical Tools to Manage Business Finances": "Получите все важные инструменты для управления финансами бизнеса", "Manage Accounting Quickly and Easily": "Управляйте бухгалтерией быстро и легко", "Manage your billing and accounting without little to no effort! Set financial goals and let the system monitor them for you, automate taxes, and more! - without lifting a finger.": "Управляйте своими счетами и бухгалтерским учетом практически без усилий! ", "Handle Inventory Tasks Without Stress": "Выполняйте задачи инвентаризации без стресса", "Easily manage inventory by creating categories and adding products to them. Modify product prices whenever you want, assign SKUs, create different tax rates, and do so much more!": "Легко управляйте запасами, создавая категории и добавляя в них продукты. ", "Take Your Project From Proposal To Payment": "Проведите свой проект от предложения до оплаты", "Create proposal templates and pitch your future clients. Turn your accepted proposals into payable invoices, send reminders, and get paid fast - all in one place!": "Создавайте шаблоны предложений и предлагайте их своим будущим клиентам. ", "Reports": "Отчеты", "Get a report on transactions with an easy filtering option. Download the required account statements in either PDF, CSV, or Excel format. You get duly prepared reports on individual Income, Expenses, Tax, Invoice, and bill summary. Filter them based on Account, category, and customers. Also, a graphical display of the Income VS Expense chart along with a detailed calculation of Profit and Loss will help you make informed decisions. Filter the tax summary and Income VS Expense chart based on financial years.": "Получите отчет о транзакциях с удобной фильтрацией. ", "Budget Planner": "Планировщик бюджета", "A budget is a financial plan for a specified period to keep in check with the working capital. This feature here helps to maintain the capital flow. You can set monthly, quarterly, half-yearly, or yearly budgets according to your business plans and needs. The main categories are “Income” and “Expense” where one can edit /update /delete the sub-categories as well.": "Бюджет — это финансовый план на определенный период, позволяющий контролировать оборотный капитал. ", "Why choose dedicated modules": "Почему стоит выбирать выделенные модули", "for your business?": "для вашего бизнеса?", "With Dash, you can conveniently manage all your business functions from a single location": "С помощью Dash вы можете удобно управлять всеми функциями вашего бизнеса из одного места.", "Empower Your Workforce with DASH": "Расширьте возможности своих сотрудников с помощью DASH", "Access over Premium Add-ons for Accounting, HR, Payments, Leads, Communication, Management, and more, all in one place!": "Доступ к надстройкам премиум-класса для бухгалтерского учета, управления персоналом, платежей, потенциальных клиентов, коммуникаций, управления и многого другого — все в одном месте!", "Pay-as-you-go": "Оплата по мере использования", "Unlimited installation": "Неограниченная установка", "Secure cloud storage": "Безопасное облачное хранилище", "Monthly": "Ежемесячно", "Yearly": "Ежегодно", "Billed monthly, or": "Оплата помесячно или", " if paid monthly": " если платить ежемесячно", "for Your Business?": "для вашего бизнеса?", "With Dash, you can conveniently manage all your business functions from a single location.": "С помощью Dash вы можете удобно управлять всеми функциями вашего бизнеса из одного места.", "Customers": "Клиенты", "Vendors": "Продавцы", "Invoices": "Счета-фактуры", "Bills": "Счета", "Account Balance": "Ба<PERSON><PERSON><PERSON><PERSON> счета", "Holder Name": "Имя держателя", "there is no account balance": "нет баланса на счете", "Cashflow": "Денежный поток", "Income Today": "Доход сегодня", "Expense Today": "Расходы сегодня", "Income This Month": "Доход в этом месяце", "Expense This Month": "Расходы в этом месяце", "Income & Expense": "Доходы и расходы", "Current Year": "Текущий год", "Income By Category": "Доход по категориям", "Year": "Год", "Latest Income": "Последний доход", "Amount Due": "Сумма к оплате", "Expense By Category": "Расходы по категориям", "Latest Expense": "Последние расходы", "Recent Invoices": "Последние счета", "Issue Date": "Дата выпуска", "Invoices Weekly Statistics": "Еженедельная статистика счетов", "Invoices Monthly Statistics": "Ежемесячная статистика счетов", "Invoice Generated": "Счет создан", "Paid": "Оплаченный", "Due": "Должный", "Recent Bills": "Недавние законопроекты", "Bills Weekly Statistics": "Еженедельная статистика по счетам", "Bills Monthly Statistics": "Ежемесячная статистика по счетам", "Bill Generated": "<PERSON><PERSON><PERSON><PERSON> сгене<PERSON><PERSON>р<PERSON>ан", "Months": "Месяцы", "Download": "Скачать", "Duration": "Продолжительность", "Revenue :": "Доход :", "Invoice :": "Счет :", "Income = Revenue + Invoice :": "Доход = Выручка + Счет:", "Quarterly": "Ежеквартальный", "Monthly Cashflow": "Ежемесячный денежный поток", "Revenue : ": "Доход : ", "Invoice : ": "Счет : ", "Total Income =  Revenue + Invoice ": "Общий доход = Выручка + Счет ", "Total Income": "Общий доход", "PaySlip": "расчетный лист", "Total Expense =  Payment + PaySlip + Bill ": "Общие расходы = Оплата + Платежная ведомость + Счет. ", "Total Expenses": "Общие расходы", "Net Profit = Total Income - Total Expense": "Чистая прибыль = Общий доход - Общие расходы", "Net Profit": "Чистая прибыль", "Income Vs Expense Summary": "Сводная информация о доходах и расходах", "Profit": "Выгода", "Type": "Тип", "Income : ": "Доход : ", "Expense : ": "Расход: ", "Purchase": "Покупка", "Employee Salary": "Заработная плата сотрудников", "Profit = Income - Expense ": "Прибыль = Доходы - Расходы ", "Income tax not found": "Налог на прибыль не найден", "Expense tax not found": "Налог на расходы не найден", "Payment :": "Оплата :", "Bill :": "Счет :", "Purchase :": "Покупка :", "Employee Salary :": "Заработная плата сотрудников:", "Expense = Payment + Bill + Employee Salary :": "Расход = Оплата + Счет + Зарплата сотрудника:", "Expense = Payment + Bill + Purchase :": "Расход = Оплата + Счет + Покупка:", "Product Stock Report": "Отчет о запасах продукции", "Product Name": "Название продукта", "Product & Service Module is Off": "Модуль продуктов и услуг выключен", "Profit & Loss Summary": "Сводная информация о прибылях и убытках", "Profit && Loss Summary": "Сводка о прибылях и убытках", "Payment : ": "Оплата : ", "Bill : ": "Счет : ", "Total Expense =  Payment + Bill + Employee Salary": "Общие расходы = Оплата + Счет + Зарплата сотрудника", "Total Expense =  Payment + Bill": "Общие расходы = Платеж + Счет", "Net Profit = Total Income - Total Expense ": "Чистая прибыль = Общий доход - Общие расходы ", "Quarterly Cashflow": "Ежеквартальный денежный поток", "Start Month": "Начальный месяц", "End Month": "Конец месяца", "of": "из", "Total Invoice": "<PERSON><PERSON><PERSON><PERSON> счет", "Total Paid": "Всего оплачено", "Total Due": "Общая сумма задолженности", "Summary": "Краткое содержание", "\tPaid Amount": "\tОплаченная сумма", "Payment Date": "Дата платежа", "Account Statement Summary": "Сводная информация о выписке со счета", "Bill / Purchase": "Счет/Покупка", "Total Bill": "<PERSON><PERSON><PERSON><PERSON> счет", "Paid Amount": "Оплаченная сумма", "Bank Details": "Банковские реквизиты", "Bank Name": "Название банка", "Please On Your Bank Account!": "Пожалуйста, на ваш банковский счет!", "Click Here": "Кликните сюда", " Remove": " Удалять", "Choose file here": "Выберите файл здесь", "Edit Payment": "Редактировать платеж", "Bank Account": "Банковский счет", "This field is required": "Это поле обязательно к заполнению", "first, make a payment and take a screenshot or download the receipt and upload it.": "сначала совершите платеж и сделайте снимок экрана или скачайте чек и загрузите его.", "Please correct the errors and try again.": "Пожалуйста, исправьте ошибки и повторите попытку.", "Close": "Закрывать", "Make Payment": "Произвести оплату", "Manage Payments": "Управление платежами", "Setup": "Настраивать", "Create New Payment": "Создать новый платеж", "Invoice Number": "Номер счета", "Order Id": "Идентификатор заказа", "status": "статус", "Payment Type": "Тип оплаты", "Bank Detail": "Банковские реквизиты", "Account Holder Name": "Имя владельца счета", "Attachment": "Вложение", "Preview": "Предварительный просмотр", "Not Found": "Не найдено", "Approved": "Одобренный", "Reject": "Отклонять", "Basic Info": "Основная информация", "Enter Customer Name": "Введите имя клиента", "Enter Contact": "Введите контакт", "Tax Number": "Налоговый номер", "Enter Tax Number": "Введите налоговый номер", "Billing Address": "Адрес для выставления счета", "Enter Name": "Введите имя", "Phone": "Телефон", "Enter Phone": "Введите телефон", "Address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enter Address": "Введите адрес", "City": "Город", "Enter City": "Введите город", "State": "Состояние", "Enter State": "Введите штат", "Country": "Страна", "Enter Country": "Введите страну", "Zip Code": "Почтовый индекс", "Enter Zip Code": "Введите почтовый индекс", "Shipping Same As Billing": "Доставка такая же, как и выставление счета", "Shipping Address": "Адрес доставки", "View": "Вид", "Edit Customer": "Изменить клиента", "Manage Customers": "Управление клиентами", "Customer Import": "Импорт клиентов", "Import": "Импорт", "Grid View": "Представление в виде сетки", "Create New Customer": "Создать нового клиента", "Customer Statement": "Заявление клиента", "Statement": "Заявление", "From Date": "С даты", "Until Date": "До даты", "My Company": "Моя компания", "Statement of Account": "Выписка со счета", "Billed To": "Кому выставлен счет", "Tax Number ": "Налоговый номер ", "Shipped To": "Отправлено в", "No Data Found": "Данные не найдены", "TOTAL :": "ОБЩИЙ :", "Download Sample Customer CSV File": "Скачать образец CSV-файла клиента", "Select CSV File": "Выберите CSV-файл", "Upload": "Загрузить", "Import Customer CSV Data": "Импорт данных клиента в формате CSV", "Customer-Detail": "Сведения о клиенте", "Create Invoice": "Создать счет", "Create Proposal": "Создать предложение", "Create retainer": "Создать гонорар", "Details": "Подробности", "Proposals": "Предложения", "Customer Info": "Информация о клиенте", "Billing Info": "Платежная информация", "Shipping Info": "Информация о доставке", "Company Info": "Информация о компании", "Customer Id": "Идентификатор клиента", "Total Sum of Invoices": "Общая сумма счетов", "Date of Creation": "Дата создания", "Quantity of Invoice": "Количество счетов-фактур", "Average Sales": "Средний объем продаж", "Overdue": "Просрочено", "Proposal": "Предложение", "Convert to Invoice": "Преобразовать в счет-фактуру", "Already convert to Invoice": "Уже конвертировано в счет-фактуру", "Duplicate": "Дублировать", "You want to confirm duplicate this invoice. Press Yes to continue or Cancel to go back": "Вы хотите подтвердить дубликат этого счета. ", "Show": "Показывать", "Detail": "Деталь", "Duplicate Invoice": "Дубликат счета", "You want to confirm this action. Press Yes to continue or Cancel to go back": "Вы хотите подтвердить это действие. ", "Edit Revenue": "Изменить доход", "Account summary": "Сводка по аккаунту", "Invoiced amount": "Сумма счета", "Amount Paid": "Оплаченная сумма", "Balance Due": "Остаток к оплате", "Please add contact number with country code. (ex. +91)": "Пожалуйста, добавьте контактный номер с кодом страны. ", "Password": "Пароль", "List View": "Просмотр списка", "Edit customer": "Изменить клиента", "Customer Details": "Сведения о клиенте", "Code": "<PERSON>од", "Is Enabled": "Включено", "Manage Chart of Accounts": "Управление планом счетов", "Chart of Account": "<PERSON><PERSON><PERSON><PERSON> счетов", "Create New Account": "Создать новую учетную запись", "Start Date": "Дата начала", "End Date": "Дата окончания", "Parent Account Name": "Имя родительской учетной записи", "Enabled": "Включено", "Disabled": "Неполноценный", "Edit Account": "Редактировать аккаунт", "Account Drilldown Report": "Подробный отчет об аккаунте", "Account Drilldown": "Детализация аккаунта", "Account Name": "Имя учетной записи", "Account Code": "Код счета", "Transaction Type": "Тип транзакции", "Transaction Date": "Дата транзакции", "Debit": "Де<PERSON><PERSON><PERSON>", "Credit": "Кредит", "Make this a sub-account": "Сделать это дополнительным аккаунтом", "Parent Account": "Родительский аккаунт", "Bill Edit": "Билл Править", "something went wrong please try again": "что-то пошло не так, попробуйте еще раз", "Project": "Проект", "Select Account Type": "Выберите тип учетной записи", "Projects": "Проекты", "Please create vendor/Client first.": "Сначала создайте поставщика/клиента.", "Create vendor/Client": "Создать поставщика/клиента", "Billing Type": "Тип платежа", "Select Billing Type": "Выберите тип платежа", "Item Wise": "Мудрый предмет", "Project Wise": "Проект Мудрый", "bill Number": "номер счета", "Order Number": "Номер заказа", "Manage Bill": "Управлять Биллом", "Link Copy on Clipboard": "Копирование ссылки в буфер обмена", "Copy": "Копировать", "Click to copy Bill link": "Нажмите, чтобы скопировать ссылку на Билл", "BILL": "СЧЕТ", "Registration Number": "Регистрационный номер", "Number": "Число", "Number: ": "Число: ", "Issue Date:": "Дата выпуска:", "Bill To": "Законопроект о", "Ship To": "Корабль", "Item": "Элемент", "Rate": "Ставка", "Subtotal": "Итого", " FROM": " ОТ", "From:": "От:", "FROM": "ОТ", "Manage Bills": "Управление счетами", "Bill Detail": "Подробности счета", "Add Debit Note": "Добавить дебетовое примечание", "Resend Bill": "Отправить счет повторно", "Send Bill": "Отправить счет", "SendMail": "ОтправитьПочту", "Item Summary": "Краткое описание товара", "All items here cannot be deleted.": "Все элементы здесь не могут быть удалены.", "after discount & tax": "после скидки и налога", "Payment Summary": "Сводка платежей", "Debit Note Summary": "Сводка дебетового авизо", "Success": "Успех", "copy": "копировать", "Click to copy invoice link": "Нажмите, чтобы скопировать ссылку на счет-фактуру", "Create Bill": "Создать счет", "Created on ": "Создано ", "Sent on": "Отправлено", "Not Sent": "Не отправлено", "Mark Sent": "Марк Сент", "Send": "Отправлять", "Pay Bill": "Оплатить счет", "Awaiting payment": "Ожидание оплаты", "Add Payment": "Добавить платеж", "Apply Debit Note": "Применить дебетовое авизо", "Send Mail": "Отправить почту", "Account Amount": "Сумма счета", "Debit note Applied": "Дебетовое авизо Применено", "Debit note issued": "Дебетовое авизо выдано", "Attachments": "Вложения", "Drop files here to upload": "Перетащите сюда файлы для загрузки", "File Name": "Имя файла", "File Size": "Размер файла", "Date Created": "Дата создания", "Bill Create": "Билл Создать", "vendor": "продавец", "Bill Number": "Номер счета", "Please add constant category. ": "Пожалуйста, добавьте постоянную категорию. ", "Add Category": "Добавить категорию", "Click to copy bill link": "Нажмите, чтобы скопировать ссылку на счет", "Click here to add Bill": "Нажмите здесь, чтобы добавить Билла", "Bill to": "Законопроект о", "Ship to": "Корабль", "BIlling Address": "Адрес для выставления счета", "Edit Vendor": "Изменить поставщика", "Manage Vendors": "Управление поставщиками", "Vendor Import": "Импорт поставщиков", "Create New Vendor": "Создать нового поставщика", "Vendor Statement": "Заявление поставщика", "Download Sample Vendor CSV File": "Загрузить образец CSV-файла поставщика", "Import Vendor CSV Data": "Импорт данных поставщика в формате CSV", "Vendor-Detail": "Информация о поставщике", "Vendor Info": "Информация о продавце", "Vendor Id": "Идентификатор поставщика", "Total Sum of Bills": "Общая сумма счетов", "Quantity of Bills": "Количество купюр", "Duplicate Bill": "Дубликат счета", "Billed amount": "Сумма к оплате", "Edit vendor": "Изменить поставщика", "vendor Details": "Сведения о поставщике", "Bank Type": "Ти<PERSON> банка", "Paypal": "<PERSON>а<PERSON><PERSON><PERSON>л", "Stripe": "Полоса", "Bank Holder Name": "Имя владельца банка", "Enter Bank Holder Name": "Введите имя владельца банка", "Enter Bank Name": "Введите название банка", "Enter Account Number": "Введите номер счета", "Opening Balance": "Начальный баланс", "Enter Opening Balance": "Введите начальный баланс", "Enter Contact Number": "Введите контактный номер", "Enter Bank Branch": "Войти в отделение банка", "Enter Swift Number": "Введите номер Swift", "Enter Bank Address": "Введите адрес банка", "Edit Bank Account": "Изменить банковский счет", "Manage Bank Account": "Управление банковским счетом", "Transaction Summary": "Сводка транзакции", "Manage Revenues": "Управляйте доходами", "Revenues": "Доходы", "Create New Revenue": "Создать новый доход", "Income Account": "Счет доходов", "Expense Account": "Счет расходов", "Customer Prefix": "Префикс клиента", "Vendor Prefix": "Префикс поставщика", "Edit your Company Bill details": "Измените данные счета вашей компании", "Prefix": "Префикс", "Starting Number": "Стартовый номер", "Footer Title": "Название нижнего колонтитула", "Footer Notes": "Примечания в нижнем колонтитуле", "Shipping Display?": "Дисплей доставки?", "QR Display?": "QR-дисплей?", "Bill Template": "Ша<PERSON><PERSON><PERSON>н счета", "Color Input": "Цветовой ввод", "Bill Logo": "Лого<PERSON><PERSON><PERSON> Билла", "Edit Bank Accounts settings": "Изменить настройки банковских счетов", "Account number": "Номер счета", "Enable/Disable": "Включить/отключить"}